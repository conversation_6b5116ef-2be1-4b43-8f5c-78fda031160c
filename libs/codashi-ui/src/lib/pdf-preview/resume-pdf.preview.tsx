import { Document, Page, PDFViewer } from '@react-pdf/renderer';
import { exhaustive } from 'exhaustive';
import { type FC } from 'react';

import { type Resume } from '@awe/codashi-core';
import { PdfResumeAwards } from './sections/awards';
import { PdfResumeCertificates } from './sections/certificates';
import { PdfResumeEducation } from './sections/education';
import { PdfResumeHeader } from './sections/header';
import { PdfResumeInterests } from './sections/interests';
import { PdfResumeLanguages } from './sections/languages';
import { PdfResumeProjects } from './sections/projects';
import { PdfResumePublications } from './sections/publications';
import { PdfResumeReferences } from './sections/references';
import { PdfResumeSkills } from './sections/skills';
import { PdfResumeVolunteer } from './sections/volunteer';
import { PdfResumeWork } from './sections/work';
import { theme } from './theme';

type Props = {
  profile: Resume;
  height?: number | string;
};

export const ResumePdfPreview: FC<Props> = ({ profile, height = '100%' }) => {
  return (
    <div style={{ width: '100%', height }}>
      <PDFViewer style={{ width: '100%', height }}>
        <Document>
          <Page
            style={{
              padding: 10,
              display: 'flex',
              flexDirection: 'column',
              gap: 8,
              fontSize: 12,
              color: theme.colors.text.primary,
            }}
          >
            <PdfResumeHeader section={profile.header} />

            {profile.sections.map((section, index) => {
              if (!section.items.length) return null;

              return exhaustive(section.name, {
                work: () => (
                  <PdfResumeWork
                    section={
                      section as Extract<typeof section, { name: 'work' }>
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
                volunteer: () => (
                  <PdfResumeVolunteer
                    section={
                      section as Extract<typeof section, { name: 'volunteer' }>
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
                education: () => (
                  <PdfResumeEducation
                    section={
                      section as Extract<typeof section, { name: 'education' }>
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
                awards: () => (
                  <PdfResumeAwards
                    section={
                      section as Extract<typeof section, { name: 'awards' }>
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
                certificates: () => (
                  <PdfResumeCertificates
                    section={
                      section as Extract<
                        typeof section,
                        { name: 'certificates' }
                      >
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
                publications: () => (
                  <PdfResumePublications
                    section={
                      section as Extract<
                        typeof section,
                        { name: 'publications' }
                      >
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
                skills: () => (
                  <PdfResumeSkills
                    section={
                      section as Extract<typeof section, { name: 'skills' }>
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
                languages: () => (
                  <PdfResumeLanguages
                    section={
                      section as Extract<typeof section, { name: 'languages' }>
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
                interests: () => (
                  <PdfResumeInterests
                    section={
                      section as Extract<typeof section, { name: 'interests' }>
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
                references: () => (
                  <PdfResumeReferences
                    section={
                      section as Extract<typeof section, { name: 'references' }>
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
                projects: () => (
                  <PdfResumeProjects
                    section={
                      section as Extract<typeof section, { name: 'projects' }>
                    }
                    key={`${section.name}-${index}`}
                  />
                ),
              });
            })}
          </Page>
        </Document>
      </PDFViewer>
    </div>
  );
};
